name: AI - Issue

on:
  issues:
    types: [opened]

permissions:
  contents: write
  pull-requests: write
  actions: read

jobs:
  attempt-fix:
    runs-on: ubuntu-latest
    timeout-minutes: 60

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Cursor CLI
        run: |
          curl https://cursor.com/install -fsS | bash
          echo "$HOME/.cursor/bin" >> $GITHUB_PATH

      - name: Configure git identity
        run: |
          git config user.name "Cursor Agent"
          git config user.email "<EMAIL>"

      - name: Analyze Issue
        env:
          CURSOR_API_KEY: ${{ secrets.CURSOR_API_KEY }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          cursor-agent -p "You are operating in a GitHub Actions runner to automatically analyze and fix GitHub issues.

          The GitHub CLI is available as \`gh\` and authenticated via \`GH_TOKEN\`. Git is available.

          # Context:
          - Repository: ${{ github.repository }}
          - Default Branch: ${{ github.event.repository.default_branch }}
          - Issue Number: ${{ github.event.issue.number }}
          - Issue Title: ${{ github.event.issue.title }}
          - Issue Author: ${{ github.event.issue.user.login }}
          - Issue Body: ${{ github.event.issue.body }}
          - Issue Labels: ${{ join(github.event.issue.labels.*.name, ', ') }}

          # Goal:
          - Analyze the issue and determine if it can be automatically fixed.
          - If a fix is possible, create a new branch from the default branch, make the changes, and push them to the repository.
          - Create a pull request from the new branch to the default branch.
          - If a fix is not possible, leave a comment on the issue indicating that a manual fix is required.

          # Guidelines:
          - Only attempt fixes for clear, well-defined issues.
          - Avoid making changes that could break existing functionality.
          - Be conservative - when in doubt, request manual review.
          - Follow the project's coding standards and conventions." \
          --force --model gpt-5 --output-format=text
